import React, { useState } from 'react'; 
 
const HelpReference = ({ isOpen, onClose }) => { 
  const [searchQuery, setSearchQuery] = useState(''); 
  const [activeTab, setActiveTab] = useState('buttons'); 

  // If not open, don't render
  if (!isOpen) return null;
 
  // Categories for the tabs 
  const categories = [ 
    { id: 'buttons', label: 'Buttons' }, 
    { id: 'forms', label: 'Forms' }, 
    { id: 'navigation', label: 'Navigation' }, 
    { id: 'layout', label: 'Layout' }, 
    { id: 'graphics', label: 'Graphics' } 
  ]; 
 
  // Component examples for each category 
  const componentExamples = { 
    buttons: [ 
      { 
        id: 'primary-button', 
        title: 'Primary Button', 
        preview: { type: 'button', variant: 'primary', label: 'Click Me' }, 
        characteristics: [ 
          'Solid fill with brand color', 
          'High contrast between text and background', 
          'Usually represents the primary action' 
        ] 
      }, 
      { 
        id: 'secondary-button', 
        title: 'Secondary Button', 
        preview: { type: 'button', variant: 'secondary', label: 'Cancel' }, 
        characteristics: [ 
          'Outlined with brand color, white or transparent fill', 
          'Less visual weight than primary buttons', 
          'Often used for secondary actions (cancel, back, etc.)' 
        ] 
      }, 
      { 
        id: 'text-button', 
        title: 'Text Button', 
        preview: { type: 'button', variant: 'text', label: 'Learn More' }, 
        characteristics: [ 
          'No background or border, only text in brand color', 
          'Lowest visual weight among button types', 
          'Used for tertiary actions or in space-constrained areas' 
        ] 
      } 
    ], 
    forms: [ 
      { 
        id: 'text-input', 
        title: 'Text Input', 
        preview: { type: 'input', placeholder: 'Enter your name' }, 
        characteristics: [ 
          'Single-line text field', 
          'May include placeholder text', 
          'Often has labels above or floating within' 
        ] 
      }, 
      { 
        id: 'checkbox', 
        title: 'Checkbox', 
        preview: { type: 'checkbox', label: 'I agree to the terms' }, 
        characteristics: [ 
          'Square box that can be checked/unchecked', 
          'Used for boolean choices or multiple selections', 
          'Label usually appears to the right' 
        ] 
      } 
    ], 
    navigation: [ 
      { 
        id: 'navbar', 
        title: 'Navigation Bar', 
        preview: { type: 'navbar' }, 
        characteristics: [ 
          'Horizontal bar typically at the top of the page', 
          'Contains navigation links and possibly logo, search, etc.', 
          'May be fixed or scrollable' 
        ] 
      }, 
      { 
        id: 'sidebar', 
        title: 'Sidebar Navigation', 
        preview: { type: 'sidebar' }, 
        characteristics: [ 
          'Vertical navigation panel on the side of the page', 
          'May be fixed, collapsible, or responsive', 
          'Often contains hierarchical navigation items' 
        ] 
      } 
    ], 
    layout: [ 
      { 
        id: 'card', 
        title: 'Card', 
        preview: { type: 'card' }, 
        characteristics: [ 
          'Contained component with a distinct visual boundary', 
          'Often has a shadow or border to separate from background', 
          'May contain various elements like images, text, buttons' 
        ] 
      }, 
      { 
        id: 'grid', 
        title: 'Grid Layout', 
        preview: { type: 'grid' }, 
        characteristics: [ 
          'Structured layout with columns and rows', 
          'Used to organize content in a consistent way', 
          'May be responsive, changing columns based on screen size' 
        ] 
      } 
    ], 
    graphics: [ 
      { 
        id: 'hero-image', 
        title: 'Hero Image', 
        preview: { type: 'hero' }, 
        characteristics: [ 
          'Large banner image often at the top of a page', 
          'May include overlay text or buttons', 
          'Used to create visual impact and convey brand message' 
        ] 
      }, 
      { 
        id: 'icon', 
        title: 'Icon', 
        preview: { type: 'icon' }, 
        characteristics: [ 
          'Small symbolic graphic representing an action or concept', 
          'Used to enhance usability and visual appeal', 
          'May be standalone or accompany text' 
        ] 
      } 
    ] 
  }; 

  // Filter examples based on search query
  const filteredExamples = componentExamples[activeTab]?.filter(example => 
    example.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    example.characteristics.some(char => char.toLowerCase().includes(searchQuery.toLowerCase()))
  ) || [];
  
  // Render preview based on component type
  const renderPreview = (preview) => {
    switch(preview.type) {
      case 'button':
        if (preview.variant === 'primary') {
          return (
            <button className="bg-blue-600 text-white px-4 py-2 rounded-md">
              {preview.label}
            </button>
          );
        } else if (preview.variant === 'secondary') {
          return (
            <button className="border border-blue-600 text-blue-600 px-4 py-2 rounded-md">
              {preview.label}
            </button>
          );
        } else {
          return (
            <button className="text-blue-600 px-4 py-2">
              {preview.label}
            </button>
          );
        }
      case 'input':
        return (
          <input 
            type="text" 
            placeholder={preview.placeholder} 
            className="border border-gray-300 rounded-md px-3 py-2 w-full"
          />
        );
      case 'checkbox':
        return (
          <div className="flex items-center">
            <input type="checkbox" className="mr-2" />
            <span>{preview.label}</span>
          </div>
        );
      case 'navbar':
        return (
          <div className="bg-gray-800 text-white p-3 rounded-md flex justify-between w-full">
            <div>Logo</div>
            <div className="flex space-x-4">
              <span>Home</span>
              <span>About</span>
              <span>Contact</span>
            </div>
          </div>
        );
      case 'sidebar':
        return (
          <div className="bg-gray-100 p-3 rounded-md w-40">
            <div className="mb-2 font-bold">Menu</div>
            <div className="space-y-2">
              <div className="pl-2">Dashboard</div>
              <div className="pl-2">Profile</div>
              <div className="pl-2">Settings</div>
            </div>
          </div>
        );
      case 'card':
        return (
          <div className="border border-gray-200 rounded-md shadow-sm p-4 w-full">
            <div className="font-bold mb-2">Card Title</div>
            <p className="text-sm text-gray-600 mb-3">This is a card component that contains content.</p>
            <button className="text-blue-600 text-sm">Read more</button>
          </div>
        );
      case 'grid':
        return (
          <div className="grid grid-cols-3 gap-2 w-full">
            <div className="bg-gray-100 p-2 rounded-md">Item 1</div>
            <div className="bg-gray-100 p-2 rounded-md">Item 2</div>
            <div className="bg-gray-100 p-2 rounded-md">Item 3</div>
            <div className="bg-gray-100 p-2 rounded-md">Item 4</div>
            <div className="bg-gray-100 p-2 rounded-md">Item 5</div>
            <div className="bg-gray-100 p-2 rounded-md">Item 6</div>
          </div>
        );
      case 'hero':
        return (
          <div className="bg-blue-500 text-white p-6 rounded-md w-full">
            <h2 className="text-xl font-bold mb-2">Hero Title</h2>
            <p className="mb-4">This is a hero section with a call to action.</p>
            <button className="bg-white text-blue-500 px-4 py-2 rounded-md">Learn More</button>
          </div>
        );
      case 'icon':
        return (
          <div className="flex space-x-4">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-gray-600">i</span>
            </div>
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-gray-600">✓</span>
            </div>
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-gray-600">×</span>
            </div>
          </div>
        );
      default:
        return <div>Preview not available</div>;
    }
  };
 
  return ( 
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"> 
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col"> 
        <div className="bg-gray-100 p-4 rounded-t-lg border-b border-gray-200 flex justify-between items-center"> 
          <h2 className="text-xl font-bold text-gray-800">UI Component Reference Guide</h2> 
          <button  
            className="text-gray-500 hover:text-gray-700" 
            onClick={onClose} 
          > 
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> 
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /> 
            </svg> 
          </button> 
        </div> 
         
        <div className="p-4 border-b border-gray-200"> 
          <div className="relative"> 
            <input  
              type="text"  
              placeholder="Search components..."  
              className="w-full p-2 pl-3 pr-10 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
              value={searchQuery} 
              onChange={(e) => setSearchQuery(e.target.value)} 
            /> 
            <div className="absolute right-3 top-2 text-gray-400"> 
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"> 
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /> 
              </svg> 
            </div> 
          </div> 
        </div> 
         
        <div className="flex flex-1 overflow-hidden"> 
          {/* Category Tabs */} 
          <div className="w-48 bg-gray-50 border-r border-gray-200 overflow-y-auto"> 
            {categories.map(category => ( 
              <button 
                key={category.id} 
                className={`w-full text-left p-3 ${activeTab === category.id ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600' : 'text-gray-700 hover:bg-gray-100'}`} 
                onClick={() => setActiveTab(category.id)} 
              > 
                {category.label} 
              </button> 
            ))} 
          </div> 
           
          {/* Component Examples */} 
          <div className="flex-1 overflow-y-auto p-4"> 
            {filteredExamples.length > 0 ? ( 
              filteredExamples.map(example => ( 
                <div key={example.id} className="mb-8 border border-gray-200 rounded-md overflow-hidden"> 
                  <div className="bg-gray-50 p-3 border-b border-gray-200"> 
                    <h3 className="font-bold text-gray-800">{example.title}</h3> 
                  </div> 
                   
                  <div className="p-4 flex flex-col md:flex-row"> 
                    {/* Preview */} 
                    <div className="md:w-1/3 mb-4 md:mb-0 flex items-center justify-center p-4 bg-white border border-gray-100 rounded-md"> 
                      {renderPreview(example.preview)} 
                    </div> 
                     
                    {/* Characteristics */} 
                    <div className="md:w-2/3 md:pl-6"> 
                      <h4 className="font-medium text-gray-700 mb-2">Key Characteristics:</h4> 
                      <ul className="list-disc pl-5 space-y-1"> 
                        {example.characteristics.map((char, idx) => ( 
                          <li key={idx} className="text-gray-600">{char}</li> 
                        ))} 
                      </ul> 
                    </div> 
                  </div> 
                </div> 
              )) 
            ) : ( 
              <div className="text-center py-8 text-gray-500"> 
                {searchQuery ? 'No components match your search.' : 'No components available in this category.'} 
              </div> 
            )} 
          </div> 
        </div> 
         
        <div className="bg-gray-50 p-4 border-t border-gray-200 flex justify-end"> 
          <button 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700" 
            onClick={onClose} 
          > 
            Close Reference 
          </button> 
        </div> 
      </div> 
    </div> 
  ); 
}; 
 
export default HelpReference;
