import { createContext, useState, useEffect, useContext } from 'react';
import { api } from '../config/api';

// Create the authentication context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is already logged in - only on initial mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // First check if we have an auth token
        const authToken = localStorage.getItem('auth_token');
        if (!authToken) {
          console.log('No auth token found, user is not authenticated');
          setUser(null);
          setLoading(false);
          return;
        }

        // If we have a token, check if we have cached user data
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          try {
            const parsedUser = JSON.parse(storedUser);
            console.log('Found user in localStorage:', parsedUser);
            // Set the user from localStorage immediately to avoid login loop
            setUser(parsedUser);
          } catch (e) {
            console.error('Error parsing stored user:', e);
            localStorage.removeItem('user');
          }
        }

        // Then verify with the server using the token
        console.log('Checking authentication status with server...');
        const response = await api.getCurrentUser();
        console.log('Auth status response:', response);

        if (response.success && response.user) {
          console.log('User is authenticated:', response.user);
          setUser(response.user);
          localStorage.setItem('user', JSON.stringify(response.user));
        } else if (response.auth_required) {
          // Token is invalid or expired
          console.log('Server says token is invalid or expired');
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          setUser(null);
        } else {
          // Keep using the stored user if we have one
          console.log('Using stored user data');
        }
      } catch (err) {
        console.error('Error checking auth status:', err);
        // If server is unreachable, keep using localStorage user if available
        console.log('Server unreachable, keeping localStorage user if available');
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  // Login function
  const login = async (contactNumber, password) => {
    setLoading(true);
    setError(null);
    try {
      console.log('Attempting login for:', contactNumber);
      const response = await api.login(contactNumber, password);
      console.log('Login response:', response);

      if (response.success) {
        console.log('Login successful, user:', response.user);

        // Store user in localStorage first to ensure it's available immediately
        localStorage.setItem('user', JSON.stringify(response.user));

        // Then update state
        setUser(response.user);

        // Skip session verification to avoid potential issues
        // The next page load will verify the session anyway

        return { success: true };
      } else {
        console.error('Login failed:', response.error);
        setError(response.error || 'Login failed');
        return { success: false, error: response.error || 'Login failed' };
      }
    } catch (err) {
      console.error('Error during login:', err);
      const errorMessage = err.message || 'An error occurred during login';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (contactNumber, password) => {
    setLoading(true);
    setError(null);
    try {
      const response = await api.register(contactNumber, password);
      if (response.success) {
        return { success: true };
      } else {
        setError(response.error || 'Registration failed');
        return { success: false, error: response.error || 'Registration failed' };
      }
    } catch (err) {
      const errorMessage = err.message || 'An error occurred during registration';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    setLoading(true);
    try {
      await api.logout();
      setUser(null);
      // Remove user and token from localStorage
      localStorage.removeItem('user');
      localStorage.removeItem('auth_token');
    } catch (err) {
      console.error('Error during logout:', err);
      // Even if the server call fails, clear local storage
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('auth_token');
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    user,
    setUser, // Expose setUser for session verification
    loading,
    error,
    login,
    register,
    logout,
    isAuthenticated: !!user
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
