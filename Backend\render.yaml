services:
  - type: web
    name: flask-backend
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: gunicorn app:app
    envVars:
      - key: API_URL
        value: https://a676-103-112-218-182.ngrok-free.app  # Replace with your actual API URL
      - key: FLASK_ENV
        value: production
      - key: MONGO_URI
        value: mongodb+srv://mihirpatel012024:<EMAIL>/?ssl=true
      - key: MONGO_DB_NAME
        value: Test
    runtime: python3
