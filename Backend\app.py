# This file is needed for Render.com deployment
# Render.com expects the Flask app to be in a file named app.py
# with the Flask app instance named 'app'

# Import the Flask app instance from main.py
from main import app

# This allows gun<PERSON> to find the app instance
# No need to call app.run() here as gun<PERSON> will handle that
if __name__ == "__main__":
    # For local testing only
    import os
    port = int(os.environ.get('PORT', 5001))
    app.run(host='0.0.0.0', port=port)
