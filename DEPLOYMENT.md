# Deployment Guide

## HTTPS Configuration for Production

This application automatically converts HTTP URLs to HTTPS when deployed in production environments. This ensures secure communication between the frontend and backend services.

### How it works

The frontend code includes automatic HTTPS conversion logic that:

1. **Detects production environment** by checking:
   - If the page is served over HTTPS (`window.location.protocol === 'https:'`)
   - If the hostname is not localhost, 127.0.0.1, or a local network IP (192.168.x.x)

2. **Automatically converts HTTP to HTTPS** for all three environment variables:
   - `VITE_API_DOMAIN`
   - `VITE_SOCKET_DOMAIN`
   - `VITE_TASKS_API_DOMAIN`

### Environment Files

- **`.env.local`** - Development environment (uses HTTP)
- **`.env.production`** - Production environment template (uses HTTPS)

### Files Modified

The following files include the HTTPS conversion logic:

1. **`src/config/api.js`** - Main API configuration with `convertToHttps()` function
2. **`src/main.jsx`** - Updated to use converted URLs for iframe communication
3. **`label1.js`** - Iframe script with HTTPS conversion for parent window variables
4. **`label2.js`** - Updated `getApiBaseUrl()` function to use HTTPS in production

### Deployment Steps

1. **For development**: Use the existing `.env.local` file with HTTP URLs
2. **For production**:
   - Update the URLs in `.env.production` to match your production domains
   - The frontend will automatically convert HTTP to HTTPS
   - Ensure your backend services support HTTPS
   - Update backend CORS configuration to include HTTPS origins (see Backend Configuration below)

### Backend Configuration

The backend (`Backend/main.py`) also needs to be updated for production HTTPS support:

1. **CORS Origins**: Update the CORS configuration to include HTTPS origins:
   ```python
   CORS(app, supports_credentials=True, origins=[
       'http://localhost:5173',  # Development
       'https://your-frontend-domain.com'  # Production
   ])
   ```

2. **Allowed Origins**: Update the allowed origins in proxy endpoints to include HTTPS URLs:
   ```python
   allowed_origin = [
       'http://localhost:5173',  # Development
       'https://your-frontend-domain.com'  # Production
   ]
   ```

### Testing

- **Local development**: URLs remain HTTP (localhost, 127.0.0.1, 192.168.x.x)
- **Production**: URLs are automatically converted to HTTPS
- Check browser console for logs showing the converted URLs

### Example

Development URL: `http://************:5001`
Production URL: `https://your-backend-domain.com:5001` (automatically converted)

This ensures seamless transition from development to production without manual URL changes.
