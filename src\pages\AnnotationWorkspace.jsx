import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { api } from '../config/api';

// Helper function to convert HTTP to HTTPS for production
const convertToHttps = (url) => {
  if (!url) return url;

  // Check if we're in production (not localhost or development)
  const isProduction = window.location.protocol === 'https:' ||
                      (window.location.hostname !== 'localhost' &&
                       window.location.hostname !== '127.0.0.1' &&
                       !window.location.hostname.startsWith('192.168.'));

  // Convert HTTP to HTTPS in production
  if (isProduction && url.startsWith('http://')) {
    return url.replace('http://', 'https://');
  }

  return url;
};

const AnnotationWorkspace = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const task = location.state?.task || {};
  const viewOnly = location.state?.viewOnly || false;

  const [zoom, setZoom] = useState(100);
  // Verification modal state no longer used for submission flow
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [labelSummary, setLabelSummary] = useState({
    totalElements: 0,
    totalLabeled: 0,
    categories: [],
  });
  const [detailedLabelData, setDetailedLabelData] = useState([]);
  // Set default visibility to 'annotated' if in viewOnly mode
  const [boxVisibility, setBoxVisibility] = useState(viewOnly ? 'annotated' : 'all'); // 'all', 'annotated', 'unannotated'

  // If no task was passed, redirect back to dashboard
  useEffect(() => {
    if (!task.url && !task.screenshot_name) {
      navigate('/');
    }
  }, [task, navigate]);

  // Request label summary when component mounts
  useEffect(() => {
    // Wait a short time for the iframe to load
    const timer = setTimeout(() => {
      requestLabelSummary();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Reference to the iframe
  const iframeRef = useRef(null);

  // Dynamic progress based on real-time label data
  const totalElements = labelSummary.totalElements || 0;
  const labeledElements = labelSummary.totalLabeled || 0;

  // Use review progress if in review status, otherwise use annotation progress
  const progressPercentage = task.status === 'review' && labelSummary.review ?
    labelSummary.review.percentage :
    (totalElements > 0 ? Math.round((labeledElements / totalElements) * 100) : 0);

  // Log the progress calculation for debugging
  if (task.status === 'review' && labelSummary.review) {
    console.log('Review progress calculation:', {
      accepted: labelSummary.review.accepted,
      total: labelSummary.review.total,
      percentage: labelSummary.review.percentage
    });
  }

  // Listen for messages from the iframe
  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data.type === 'LABEL_SUMMARY') {
        // Transform the data to match our state structure
        const summaryData = {
          totalElements: event.data.data.total,
          totalLabeled: event.data.data.labeled,
          categories: Object.entries(event.data.data.breakdown).map(([type, count]) => ({
            type,
            count,
            status: count > 0 ? 'Labeled' : 'Unlabeled'
          })).filter(category => category.type !== 'undefined'),
          // Add review status information
          review: event.data.data.review
        };

        setLabelSummary(summaryData);

        // Log the updated progress information
        if (task.status === 'review') {
          console.log('Review progress:', {
            total: summaryData.review.total,
            accepted: summaryData.review.accepted,
            percentage: summaryData.review.percentage
          });
        } else {
          console.log('Annotation progress:', {
            total: summaryData.totalElements,
            labeled: summaryData.totalLabeled,
            percentage: summaryData.totalElements > 0 ?
              Math.round((summaryData.totalLabeled / summaryData.totalElements) * 100) : 0
          });
        }
      } else if (event.data.type === 'SAVE_DETAILED_LABELS') {
        if (event.data.detailedData) {
          // Save the detailed data to the server
          saveLabelsToServer(event.data.detailedData);
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // Request label summary from iframe
  const requestLabelSummary = () => {
    if (iframeRef.current) {
      iframeRef.current.contentWindow.postMessage(
        {
          type: 'GET_LABEL_SUMMARY',
        },
        '*'
      );
    }
  };

  // Save labels to server
  const saveLabelsToServer = (labelData) => {
    api
      .saveLabels(labelData)
      .then((response) => {
        console.log('Labels saved successfully:', response);
        // Show a brief success message
        const saveBtn = document.querySelector('.save-btn');
        if (saveBtn) {
          const originalText = saveBtn.textContent;
          const originalBgColor = saveBtn.style.backgroundColor;

          // Change text and background color
          saveBtn.textContent = 'Saved';
          saveBtn.style.backgroundColor = '#90ee90'; // Light green

          setTimeout(() => {
            // Reset to original state
            saveBtn.textContent = originalText;
            saveBtn.style.backgroundColor = originalBgColor;
          }, 1500);
        }
      })
      .catch((error) => {
        console.error('Error saving labels:', error);
        // Show error message
        alert('Error saving labels. Please try again.');
      });
  };

  // Save functionality is now handled automatically when labels are applied

  // Handle submit
  const handleSubmit = async () => {
    // Check if progress is 100%
    if (progressPercentage < 100) {
      // Show warning message based on document status
      if (task.status === 'review') {
        alert(`Please accept all annotations before submitting. Current progress: ${progressPercentage}%`);
      } else {
        alert(`Please annotate all bounding boxes before submitting. Current progress: ${progressPercentage}%`);
      }
      return;
    }

    // Request label summary to update the data
    requestLabelSummary();

    // Send message to iframe to show the component menu
    if (iframeRef.current) {
      iframeRef.current.contentWindow.postMessage(
        {
          type: 'SHOW_COMPONENT_MENU',
        },
        '*'
      );
    }

    // If task status is "inprogress", call the review API
    if (task.status === 'inprogress') {
      try {
        const response = await api.submitForReview(task.url);
        console.log('Submit for review response:', response);

        if (response.success) {
          alert('Task submitted for review successfully!');
          // Navigate back to dashboard after successful submission
          navigate('/');
          return;
        } else if (response.error) {
          console.error('Error submitting for review:', response.error);
          alert(`Error submitting for review: ${response.error}`);
        }
      } catch (err) {
        console.error('Error submitting for review:', err);
        alert('An error occurred while submitting for review. Please try again.');
      }
    }

    // If task status is "review", call the submit final API
    if (task.status === 'review') {
      try {
        const response = await api.submitFinal(task.url);
        console.log('Submit final response:', response);

        if (response.success) {
          alert('Task submitted successfully!');
          // Navigate back to dashboard after successful submission
          navigate('/');
          return;
        } else if (response.error) {
          console.error('Error submitting final task:', response.error);
          alert(`Error submitting final task: ${response.error}`);
        }
      } catch (err) {
        console.error('Error submitting final task:', err);
        alert('An error occurred while submitting the final task. Please try again.');
      }
    }

    // No longer showing the verification modal
    // Just update the label summary data
    setTimeout(() => {
      requestLabelSummary();
    }, 300);
  };

  // Handle final submission (no longer used, kept for reference)
  const handleConfirmSubmission = () => {
    setShowVerificationModal(false);
    alert('Annotations submitted successfully!');
    navigate('/');
  };

  // Handle return to editing (no longer used, kept for reference)
  const handleReturnToEditing = () => {
    setShowVerificationModal(false);
  };

  // Effect to trigger box adjustment when iframe loads
  useEffect(() => {
    if (iframeRef.current) {
      const iframe = iframeRef.current;
      const handleIframeLoad = () => {
        // Give the iframe content a moment to initialize
        setTimeout(() => {
          // Set initial zoom level
          // Ensure bounding boxes are properly adjusted on load
          iframe.contentWindow.postMessage(
            {
              type: 'ADJUST_BOXES',
            },
            '*'
          );

          // Set initial box visibility based on viewOnly mode
          iframe.contentWindow.postMessage(
            {
              type: 'SET_BOX_VISIBILITY',
              visibility: viewOnly ? 'annotated' : 'all',
            },
            '*'
          );

          // Set view-only mode if needed
          if (viewOnly) {
            iframe.contentWindow.postMessage(
              {
                type: 'SET_VIEW_ONLY_MODE',
                viewOnly: true,
              },
              '*'
            );
          }

          // If task status is "review", show the component menu with accordionComponentList
          if (task.status === 'review') {
            setTimeout(() => {
              iframe.contentWindow.postMessage(
                {
                  type: 'SHOW_COMPONENT_MENU',
                },
                '*'
              );
            }, 1000);
          }
        }, 500);
      };

      iframe.addEventListener('load', handleIframeLoad);
      return () => {
        iframe.removeEventListener('load', handleIframeLoad);
      };
    }
  }, [iframeRef, viewOnly, task]);

  // Handle box visibility toggle
  const handleBoxVisibilityChange = (visibility) => {
    setBoxVisibility(visibility);
    // Send visibility state to iframe
    if (iframeRef.current) {
      iframeRef.current.contentWindow.postMessage(
        {
          type: 'SET_BOX_VISIBILITY',
          visibility: visibility,
        },
        '*'
      );
    }
  };

  // Handle reset view (both zoom and pan)
  const handleResetView = () => {
    // Send messages to iframe to reset both zoom and pan
    if (iframeRef.current) {
      // Reset zoom to 100%
      iframeRef.current.contentWindow.postMessage(
        {
          type: 'SET_ZOOM',
          zoomLevel: 100,
        },
        '*'
      );

      // Reset pan position
      iframeRef.current.contentWindow.postMessage(
        {
          type: 'RESET_PAN',
        },
        '*'
      );
    }
  };

  return (
    <div className="max-w-full flex flex-col" style={{ height: '100vh' }}>
      {/* Header Bar */}
      <div className="header border-b border-gray-200 bg-white py-2 px-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/')}
              className="flex items-center gap-2 text-gray-700 hover:text-gray-900"
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-gray-600"
              >
                <path
                  d="M19 12H5M5 12L12 19M5 12L12 5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
            <span className="text-gray-700 font-medium">https://{task.url || 'affinda.com/'}</span>
            <span className="text-gray-500 text-sm px-2 py-1 bg-gray-100 rounded-md">
              {task.status === 'review' ? 'In progress' : task.status === 'completed' ? 'Completed' : task.status === 'approved' ? 'Approved' : 'In progress'}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-gray-700 mr-2">View:</span>

            {/* Box visibility toggle */}
            <div className="box-visibility-toggle flex items-center rounded-md border border-gray-200 bg-white">
              <button
                className={`px-3 py-1 text-sm font-medium transition ${boxVisibility === 'all' ? 'bg-blue-500 text-white' : 'text-gray-700'}`}
                onClick={() => handleBoxVisibilityChange('all')}
              >
                All
              </button>
              <button
                className={`px-3 py-1 text-sm font-medium transition ${boxVisibility === 'annotated' ? 'bg-blue-500 text-white' : 'text-gray-700'}`}
                onClick={() => handleBoxVisibilityChange('annotated')}
              >
                Labelled
              </button>
              <button
                className={`px-3 py-1 text-sm font-medium transition ${boxVisibility === 'unannotated' ? 'bg-blue-500 text-white' : 'text-gray-700'}`}
                onClick={() => handleBoxVisibilityChange('unannotated')}
              >
                Not labelled
              </button>
            </div>

            {/* Progress bar */}
            <div className="ml-4 w-64">
              <div className="mb-1 text-gray-700 whitespace-nowrap text-sm">
                {progressPercentage}% • {task.status === 'review' && labelSummary.review ?
                  `${labelSummary.review?.accepted || 0}/${labelSummary.review?.total || 0} accepted` :
                  `${labeledElements}/${totalElements} labelled`}
              </div>
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full"
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
            </div>


            {/* Submit button */}
            {!viewOnly && (
              <button
                onClick={handleSubmit}
                disabled={progressPercentage < 100}
                className={`ml-2 px-4 py-1.5 ${progressPercentage < 100 ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-500 hover:bg-green-600'} text-white rounded-md flex items-center`}
                title={progressPercentage < 100 ? "Please annotate all bounding boxes before submitting" : "Submit annotations"}
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Submit
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative flex-1 w-full" style={{ overflow: 'hidden' }}>
        {/* Screenshot with interactive elements */}
        <div className="iframe-container" style={{ height: '100%', overflow: 'hidden' }}>
          <iframe
            ref={iframeRef}
            src={`${window.location.origin}/label.html?screenshot=${encodeURIComponent(
              convertToHttps(task.screenshot_name || '')
            )}&json=${encodeURIComponent(
              convertToHttps(task.json_name || '')
            )}&status=${encodeURIComponent(task.status)}`}
            className="rounded border"
            style={{
              width: '100%',
              height: '1500px', // This would be dynamic based on the actual screenshot
              border: 'none',
            }}
            title="Annotation Workspace"
          />
        </div>

        {/* Reset view control */}
        <div className="absolute right-20 bottom-3 flex items-center gap-1 rounded-lg bg-white p-2 shadow-lg">
          <button
            className="zoom-btn rounded-lg p-2 transition hover:bg-slate-100"
            onClick={handleResetView}
            title="Reset view to show all content"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2 10C2 10 4.00498 7.26822 5.63384 5.63824C7.26269 4.00827 9.5136 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.89691 21 4.43511 18.2543 3.35177 14.5M2 10V4M2 10H8"
                stroke="#64748b"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Verification Modal - No longer used, but kept for reference */}
      {false && showVerificationModal && (
        <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black p-4">
          <div className="flex max-h-[90vh] w-full max-w-2xl flex-col overflow-hidden rounded-lg bg-white shadow-xl">
            <div className="rounded-t-lg border-b border-gray-200 bg-gray-100 p-4">
              <h2 className="text-xl font-bold text-gray-800">3. Verify Your Annotations</h2>
            </div>

            <div className="overflow-auto p-6">
              <p className="mb-4 text-gray-700">
                Please review your annotations before submitting:
              </p>

              {/* Summary Table */}
              <div className="mb-6 overflow-hidden rounded-lg border">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        Component Type
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        Count
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {labelSummary.categories.map((category, index) => (
                      <tr
                        key={index}
                        className={
                          category.type === 'Unknown Element'
                            ? 'bg-yellow-50'
                            : category.type === 'Non-UI Element'
                              ? 'bg-gray-50'
                              : ''
                        }
                      >
                        <td className="px-4 py-3 text-sm text-gray-700">{category.type}</td>
                        <td className="px-4 py-3 text-sm text-gray-700">{category.count}</td>
                        <td className="px-4 py-3 text-sm">
                          {category.count > 0 ? (
                            <span className="text-green-600">Labeled</span>
                          ) : category.type === 'Unknown Element' ? (
                            <span className="text-orange-500">Marked as "Unsure"</span>
                          ) : category.type === 'Non-UI Element' ? (
                            <span className="text-gray-500">Marked as "Not a UI Element"</span>
                          ) : (
                            <span className="text-gray-400">Not Labeled</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Warning Note */}
              {labelSummary.categories.some(
                (cat) => cat.type === 'Unknown Element' && cat.count > 0
              ) && (
                  <div className="mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                    <p className="text-yellow-800">
                      <strong>Note:</strong> You have{' '}
                      {labelSummary.categories.find((cat) => cat.type === 'Unknown Element')?.count ||
                        0}{' '}
                      element(s) marked as "Unsure". You can either proceed with submission or go back
                      to review.
                    </p>
                  </div>
                )}
            </div>

            <div className="flex justify-end space-x-3 border-t border-gray-200 bg-gray-50 p-4">
              <button className="return-btn" onClick={handleReturnToEditing}>
                Return to Editing
              </button>
              <button className="submit-btn" onClick={handleConfirmSubmission}>
                Confirm Submission
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnnotationWorkspace;
