
// Helper function to convert HTTP to HTTPS for production
const convertToHttps = (url) => {
  if (!url) return url;

  // Check if we're in production (not localhost or development)
  const isProduction = window.location.protocol === 'https:' ||
                      (window.location.hostname !== 'localhost' &&
                       window.location.hostname !== '127.0.0.1' &&
                       !window.location.hostname.startsWith('192.168.'));

  // Convert HTTP to HTTPS in production
  if (isProduction && url.startsWith('http://')) {
    return url.replace('http://', 'https://');
  }

  return url;
};

// Load environment variables from parent window and convert to HTTPS for production
window.ENV_API_DOMAIN = convertToHttps(window.parent.ENV_API_DOMAIN || '');
window.ENV_SOCKET_DOMAIN = convertToHttps(window.parent.ENV_SOCKET_DOMAIN || '');
window.ENV_TASKS_API_DOMAIN = convertToHttps(window.parent.ENV_TASKS_API_DOMAIN || '');
console.log('Environment variables loaded in iframe:', {
    API_DOMAIN: window.ENV_API_DOMAIN,
    SOCKET_DOMAIN: window.ENV_SOCKET_DOMAIN,
    TASKS_API_DOMAIN: window.ENV_TASKS_API_DOMAIN
});
